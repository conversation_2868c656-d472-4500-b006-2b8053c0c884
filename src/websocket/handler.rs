use crate::types::SubscriptionType;
use crate::websocket::subscription::{ClientId, SubscriptionManager};
use crate::{BacktestError, Result};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::mpsc;
use tokio_tungstenite::tungstenite::Message;
use tracing::{debug, error, info, warn};

/// WebSocket消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WebSocketMessage {
    /// 订阅请求
    Subscribe {
        subscription: SubscriptionType,
    },
    /// 取消订阅请求
    Unsubscribe {
        subscription: SubscriptionType,
    },
    /// 心跳
    Ping,
    /// 心跳响应
    Pong,
    /// 错误消息
    Error {
        message: String,
    },
    /// 成功响应
    Success {
        message: String,
    },
    /// 数据推送
    Data {
        subscription: SubscriptionType,
        data: serde_json::Value,
    },
}

/// WebSocket处理器
pub struct WebSocketHandler {
    client_id: ClientId,
    subscription_manager: Arc<SubscriptionManager>,
    message_tx: mpsc::Sender<String>,
}

impl WebSocketHandler {
    /// 创建新的WebSocket处理器
    pub fn new(
        client_id: ClientId,
        subscription_manager: Arc<SubscriptionManager>,
        message_tx: mpsc::Sender<String>,
    ) -> Self {
        Self {
            client_id,
            subscription_manager,
            message_tx,
        }
    }
    
    /// 处理WebSocket消息
    pub async fn handle_message(&self, message: Message) -> Result<()> {
        match message {
            Message::Text(text) => {
                self.handle_text_message(text).await?;
            }
            Message::Binary(_) => {
                warn!("Received binary message from client {}, ignoring", self.client_id);
            }
            Message::Ping(payload) => {
                debug!("Received ping from client {}", self.client_id);
                self.send_pong(payload).await?;
            }
            Message::Pong(_) => {
                debug!("Received pong from client {}", self.client_id);
            }
            Message::Close(_) => {
                info!("Client {} requested close", self.client_id);
                self.handle_disconnect().await;
            }
            Message::Frame(_) => {
                // 原始帧，通常不需要处理
            }
        }
        
        Ok(())
    }
    
    /// 处理文本消息
    async fn handle_text_message(&self, text: String) -> Result<()> {
        debug!("Received text message from client {}: {}", self.client_id, text);
        
        let ws_message: WebSocketMessage = serde_json::from_str(&text)
            .map_err(|e| BacktestError::WebSocket(format!("Invalid message format: {}", e)))?;
        
        match ws_message {
            WebSocketMessage::Subscribe { subscription } => {
                self.handle_subscribe(subscription).await?;
            }
            WebSocketMessage::Unsubscribe { subscription } => {
                self.handle_unsubscribe(subscription).await?;
            }
            WebSocketMessage::Ping => {
                self.send_pong(Vec::new()).await?;
            }
            _ => {
                warn!("Unexpected message type from client {}", self.client_id);
                self.send_error("Unexpected message type".to_string()).await?;
            }
        }
        
        Ok(())
    }
    
    /// 处理订阅请求
    async fn handle_subscribe(&self, subscription_type: SubscriptionType) -> Result<()> {
        let success = self.subscription_manager.subscribe(&self.client_id, subscription_type.clone());
        
        if success {
            let response = WebSocketMessage::Success {
                message: format!("Subscribed to {:?}", subscription_type),
            };
            self.send_message(response).await?;
            info!("Client {} subscribed to {:?}", self.client_id, subscription_type);
        } else {
            let response = WebSocketMessage::Error {
                message: format!("Failed to subscribe to {:?}", subscription_type),
            };
            self.send_message(response).await?;
            error!("Failed to subscribe client {} to {:?}", self.client_id, subscription_type);
        }
        
        Ok(())
    }
    
    /// 处理取消订阅请求
    async fn handle_unsubscribe(&self, subscription_type: SubscriptionType) -> Result<()> {
        let success = self.subscription_manager.unsubscribe(&self.client_id, &subscription_type);
        
        if success {
            let response = WebSocketMessage::Success {
                message: format!("Unsubscribed from {:?}", subscription_type),
            };
            self.send_message(response).await?;
            info!("Client {} unsubscribed from {:?}", self.client_id, subscription_type);
        } else {
            let response = WebSocketMessage::Error {
                message: format!("Failed to unsubscribe from {:?}", subscription_type),
            };
            self.send_message(response).await?;
            warn!("Failed to unsubscribe client {} from {:?}", self.client_id, subscription_type);
        }
        
        Ok(())
    }
    
    /// 发送Pong消息
    async fn send_pong(&self, payload: Vec<u8>) -> Result<()> {
        // 这里应该发送Pong帧，但由于我们使用的是文本消息通道
        // 我们发送一个Pong类型的WebSocket消息
        let response = WebSocketMessage::Pong;
        self.send_message(response).await
    }
    
    /// 发送错误消息
    async fn send_error(&self, error_message: String) -> Result<()> {
        let response = WebSocketMessage::Error {
            message: error_message,
        };
        self.send_message(response).await
    }
    
    /// 发送WebSocket消息
    async fn send_message(&self, message: WebSocketMessage) -> Result<()> {
        let json = serde_json::to_string(&message)
            .map_err(|e| BacktestError::WebSocket(format!("Failed to serialize message: {}", e)))?;
        
        self.message_tx.send(json).await
            .map_err(|e| BacktestError::WebSocket(format!("Failed to send message: {}", e)))?;
        
        Ok(())
    }
    
    /// 处理客户端断开连接
    async fn handle_disconnect(&self) {
        info!("Handling disconnect for client {}", self.client_id);
        self.subscription_manager.remove_client(&self.client_id);
    }
    
    /// 获取客户端ID
    pub fn client_id(&self) -> ClientId {
        self.client_id
    }
    
    /// 获取客户端订阅信息
    pub fn get_subscriptions(&self) -> Option<std::collections::HashSet<SubscriptionType>> {
        self.subscription_manager.get_client_subscriptions(&self.client_id)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::mpsc;
    
    #[tokio::test]
    async fn test_websocket_handler() {
        let subscription_manager = Arc::new(SubscriptionManager::new());
        let (tx, mut rx) = mpsc::channel(100);
        
        let client_id = subscription_manager.add_client(tx.clone());
        let handler = WebSocketHandler::new(client_id, subscription_manager.clone(), tx);
        
        // 测试订阅消息
        let subscribe_msg = WebSocketMessage::Subscribe {
            subscription: SubscriptionType::OrderBook,
        };
        let json = serde_json::to_string(&subscribe_msg).unwrap();
        let message = Message::Text(json);
        
        handler.handle_message(message).await.unwrap();
        
        // 检查是否收到响应
        let response = rx.recv().await.unwrap();
        let parsed: WebSocketMessage = serde_json::from_str(&response).unwrap();
        
        match parsed {
            WebSocketMessage::Success { message } => {
                assert!(message.contains("Subscribed"));
            }
            _ => panic!("Expected success message"),
        }
    }
}
