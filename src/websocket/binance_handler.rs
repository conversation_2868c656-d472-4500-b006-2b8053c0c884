use crate::types::{MarketData, SubscriptionType};
use crate::websocket::subscription::{ClientId, SubscriptionManager};
use crate::{BacktestError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::mpsc;
use tokio_tungstenite::tungstenite::Message;
use tracing::{debug, error, info, warn};

/// Binance风格的WebSocket消息处理器
pub struct BinanceWebSocketHandler {
    client_id: ClientId,
    subscription_manager: Arc<SubscriptionManager>,
    message_sender: mpsc::Sender<String>,
    /// 当前订阅的流
    subscribed_streams: HashMap<String, SubscriptionType>,
}

/// Binance风格的订阅消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceSubscribeMessage {
    pub method: String,
    pub params: Vec<String>,
    pub id: Option<u64>,
}

/// Binance风格的取消订阅消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceUnsubscribeMessage {
    pub method: String,
    pub params: Vec<String>,
    pub id: Option<u64>,
}

/// Binance风格的响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceResponse {
    pub result: Option<serde_json::Value>,
    pub id: Option<u64>,
    pub error: Option<BinanceError>,
}

/// Binance错误信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceError {
    pub code: i32,
    pub msg: String,
}

/// Book Ticker数据格式（Binance兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceBookTicker {
    #[serde(rename = "e")]
    pub event_type: String,
    #[serde(rename = "E")]
    pub event_time: u64,
    #[serde(rename = "T")]
    pub transaction_time: u64,
    #[serde(rename = "s")]
    pub symbol: String,
    #[serde(rename = "u")]
    pub update_id: u64,
    #[serde(rename = "b")]
    pub best_bid_price: String,
    #[serde(rename = "B")]
    pub best_bid_qty: String,
    #[serde(rename = "a")]
    pub best_ask_price: String,
    #[serde(rename = "A")]
    pub best_ask_qty: String,
}

/// 组合流包装格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamWrapper {
    pub stream: String,
    pub data: serde_json::Value,
}

impl BinanceWebSocketHandler {
    /// 创建新的Binance WebSocket处理器
    pub fn new(
        client_id: ClientId,
        subscription_manager: Arc<SubscriptionManager>,
        message_sender: mpsc::Sender<String>,
    ) -> Self {
        Self {
            client_id,
            subscription_manager,
            message_sender,
            subscribed_streams: HashMap::new(),
        }
    }

    /// 处理WebSocket消息
    pub async fn handle_message(&mut self, message: Message) -> Result<()> {
        match message {
            Message::Text(text) => {
                self.handle_text_message(text).await?;
            }
            Message::Binary(_) => {
                warn!("Binary messages not supported");
                self.send_error("Binary messages not supported", None).await?;
            }
            Message::Ping(data) => {
                self.send_pong(data).await?;
            }
            Message::Pong(_) => {
                debug!("Received pong from client {}", self.client_id);
            }
            Message::Close(_) => {
                info!("Client {} requested close", self.client_id);
            }
            Message::Frame(_) => {
                warn!("Raw frame messages not supported");
            }
        }
        Ok(())
    }

    /// 处理文本消息
    async fn handle_text_message(&mut self, text: String) -> Result<()> {
        debug!("Received text message from client {}: {}", self.client_id, text);

        // 尝试解析为订阅消息
        if let Ok(subscribe_msg) = serde_json::from_str::<BinanceSubscribeMessage>(&text) {
            if subscribe_msg.method == "SUBSCRIBE" {
                return self.handle_subscribe(subscribe_msg).await;
            }
        }

        // 尝试解析为取消订阅消息
        if let Ok(unsubscribe_msg) = serde_json::from_str::<BinanceUnsubscribeMessage>(&text) {
            if unsubscribe_msg.method == "UNSUBSCRIBE" {
                return self.handle_unsubscribe(unsubscribe_msg).await;
            }
        }

        warn!("Unknown message format from client {}: {}", self.client_id, text);
        self.send_error("Unknown message format", None).await?;
        Ok(())
    }

    /// 处理订阅请求
    async fn handle_subscribe(&mut self, msg: BinanceSubscribeMessage) -> Result<()> {
        let mut subscribed_streams = Vec::new();

        for stream in &msg.params {
            if let Some(subscription_type) = self.parse_stream_name(stream) {
                if self.subscription_manager.subscribe(&self.client_id, subscription_type.clone()) {
                    self.subscribed_streams.insert(stream.clone(), subscription_type);
                    subscribed_streams.push(stream.clone());
                    info!("Client {} subscribed to stream: {}", self.client_id, stream);
                }
            } else {
                warn!("Unknown stream name: {}", stream);
                self.send_error(&format!("Unknown stream: {}", stream), msg.id).await?;
                return Ok(());
            }
        }

        // 发送成功响应
        let response = BinanceResponse {
            result: Some(serde_json::json!(null)),
            id: msg.id,
            error: None,
        };

        self.send_message(serde_json::to_string(&response)?).await?;
        Ok(())
    }

    /// 处理取消订阅请求
    async fn handle_unsubscribe(&mut self, msg: BinanceUnsubscribeMessage) -> Result<()> {
        for stream in &msg.params {
            if let Some(subscription_type) = self.subscribed_streams.remove(stream) {
                self.subscription_manager.unsubscribe(&self.client_id, &subscription_type);
                info!("Client {} unsubscribed from stream: {}", self.client_id, stream);
            }
        }

        // 发送成功响应
        let response = BinanceResponse {
            result: Some(serde_json::json!(null)),
            id: msg.id,
            error: None,
        };

        self.send_message(serde_json::to_string(&response)?).await?;
        Ok(())
    }

    /// 解析流名称为订阅类型
    fn parse_stream_name(&self, stream: &str) -> Option<SubscriptionType> {
        if stream.ends_with("@bookTicker") {
            Some(SubscriptionType::BookTicker)
        } else if stream.ends_with("@depth") || stream.contains("@depth") {
            Some(SubscriptionType::OrderBook)
        } else {
            None
        }
    }

    /// 发送错误消息
    async fn send_error(&self, message: &str, id: Option<u64>) -> Result<()> {
        let response = BinanceResponse {
            result: None,
            id,
            error: Some(BinanceError {
                code: -1,
                msg: message.to_string(),
            }),
        };

        self.send_message(serde_json::to_string(&response)?).await
    }

    /// 发送Pong消息
    async fn send_pong(&self, data: Vec<u8>) -> Result<()> {
        // WebSocket pong会由底层处理，这里不需要特殊处理
        debug!("Sending pong to client {}", self.client_id);
        Ok(())
    }

    /// 发送消息给客户端
    async fn send_message(&self, message: String) -> Result<()> {
        self.message_sender
            .send(message)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("Failed to send message: {}", e)))
    }

    /// 获取客户端ID
    pub fn client_id(&self) -> ClientId {
        self.client_id
    }
}

/// 将内部MarketData转换为Binance格式
pub fn convert_to_binance_format(market_data: &MarketData, symbol: &str) -> Option<serde_json::Value> {
    match market_data {
        MarketData::BookTicker(book_ticker) => {
            let binance_book_ticker = BinanceBookTicker {
                event_type: "bookTicker".to_string(),
                event_time: book_ticker.event_time,
                transaction_time: book_ticker.transaction_time,
                symbol: symbol.to_uppercase(),
                update_id: book_ticker.update_id,
                best_bid_price: format!("{:.8}", book_ticker.best_bid_price.value()),
                best_bid_qty: format!("{:.8}", book_ticker.best_bid_qty),
                best_ask_price: format!("{:.8}", book_ticker.best_ask_price.value()),
                best_ask_qty: format!("{:.8}", book_ticker.best_ask_qty),
            };
            serde_json::to_value(binance_book_ticker).ok()
        }
        _ => None,
    }
}

/// 获取流名称
pub fn get_stream_name(market_data: &MarketData, symbol: &str) -> Option<String> {
    match market_data {
        MarketData::BookTicker(_) => Some(format!("{}@bookTicker", symbol.to_lowercase())),
        MarketData::OrderBook(_) => Some(format!("{}@depth", symbol.to_lowercase())),
        _ => None,
    }
}
